<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BA-Hub 比赛记录分析器</title>
    <link rel="stylesheet" href="styles.css">


</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    <i class="fas fa-chart-line"></i>
                    BA-Hub 比赛记录分析器
                </h1>
                <p class="subtitle">分析玩家比赛记录和单位使用统计</p>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-card">
                    <h2><i class="fas fa-search"></i> 查询玩家记录</h2>
                    <form id="searchForm" class="search-form">
                        <div class="input-group">
                            <input 
                                type="text" 
                                id="steamIdInput" 
                                placeholder="请输入 Steam ID (例如: 76561198017063877)"
                                class="steam-input"
                                required
                            >
                            <button type="submit" class="search-btn" id="searchBtn">
                                <span class="btn-text">
                                    <i class="fas fa-search"></i> 查询
                                </span>
                                <span class="loading-spinner" style="display: none;">
                                    <i class="fas fa-spinner fa-spin"></i> 查询中...
                                </span>
                            </button>
                        </div>
                        <div class="error-message" id="errorMessage" style="display: none;"></div>
                    </form>
                    
                    <!-- 示例Steam ID -->
                    <div class="example-section">
                        <h4>示例 Steam ID</h4>
                        <button class="example-btn" onclick="fillExample('76561198017063877')">
                            76561198017063877
                        </button>
                    </div>
                </div>
            </section>

            <!-- 结果展示区域 -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <!-- 玩家概览 -->
                <div class="player-overview" id="playerOverview">
                    <h2><i class="fas fa-user"></i> 玩家概览</h2>
                    <div class="overview-stats" id="overviewStats">
                        <!-- 动态生成的统计数据 -->
                    </div>
                </div>

                <!-- 比赛列表 -->
                <div class="matches-section">
                    <h2><i class="fas fa-list"></i> 比赛记录</h2>
                    <div class="matches-container" id="matchesContainer">
                        <!-- 动态生成的比赛列表 -->
                    </div>
                </div>

                <!-- 单位分析 -->
                <div class="units-analysis">
                    <h2>单位使用分析</h2>
                    <div class="analysis-container" id="unitsAnalysis">
                        <!-- 动态生成的单位分析数据 -->
                    </div>
                </div>
            </section>

            <!-- 使用说明 -->
            <section class="info-section">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> 使用说明</h3>
                    <ul class="info-list">
                        <li>输入有效的 Steam ID 进行查询</li>
                        <li>系统将显示玩家的所有比赛记录</li>
                        <li>点击比赛可查看详细的单位使用数据</li>
                        <li>支持数据可视化分析和统计</li>
                        <li>支持移动端和桌面端访问</li>
                    </ul>
                    
                    <div class="features">
                        <h4>功能特色</h4>
                        <div class="feature-tags">
                            <span class="tag">数据分析</span>
                            <span class="tag">响应式设计</span>
                            <span class="tag">智能筛选</span>
                            <span class="tag">数据导出</span>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 BA-Hub 比赛记录分析器 | 数据来源: batrace.aoeiaol.top</p>
        </footer>
    </div>



    <script src="api.js"></script>

    <script src="utils.js"></script>
    <script src="main.js"></script>
</body>
</html>
