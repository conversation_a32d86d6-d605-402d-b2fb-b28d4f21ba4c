<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比赛详情 - BA-Hub 比赛记录分析器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">
                    比赛详情分析
                </h1>
                <p class="subtitle">详细的比赛数据和玩家表现分析</p>
            </div>
        </header>

        <!-- 导航栏 -->
        <nav class="navigation">
            <a href="index.html" class="nav-link">返回主页</a>
            <div class="match-info-header" id="matchInfoHeader">
                <!-- 动态生成的比赛基本信息 -->
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 加载状态 -->
            <div class="loading-section" id="loadingSection">
                <div class="loading-spinner-large">
                    <div class="spinner"></div>
                    <p>正在加载比赛详情...</p>
                </div>
            </div>

            <!-- 错误信息 -->
            <div class="error-section" id="errorSection" style="display: none;">
                <div class="error-card">
                    <h2>加载失败</h2>
                    <p id="errorMessage">无法加载比赛详情</p>
                    <button onclick="location.href='index.html'" class="back-btn">返回主页</button>
                </div>
            </div>

            <!-- 比赛详情内容 -->
            <div class="match-detail-content" id="matchDetailContent" style="display: none;">
                <!-- 比赛基本信息 -->
                <section class="match-overview">
                    <h2>比赛概览</h2>
                    <div class="match-basic-info" id="matchBasicInfo">
                        <!-- 动态生成的比赛基本信息 -->
                    </div>
                </section>

                <!-- 队伍对战分析 -->
                <section class="players-comparison">
                    <h2>队伍对战分析</h2>
                    <div class="players-grid" id="playersGrid">
                        <!-- 动态生成的队伍对战数据 -->
                    </div>
                </section>

                <!-- 详细单位分析 -->
                <section class="units-detailed-analysis">
                    <h2>单位使用详情</h2>
                    <div class="players-tabs" id="playersTabs">
                        <!-- 动态生成的玩家标签页 -->
                    </div>
                    <div class="units-content" id="unitsContent">
                        <!-- 动态生成的单位详情内容 -->
                    </div>
                </section>

                <!-- 比赛统计 -->
                <section class="match-statistics">
                    <h2>比赛统计</h2>
                    <div class="stats-grid" id="statsGrid">
                        <!-- 动态生成的统计数据 -->
                    </div>
                </section>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="footer">
            <p>&copy; 2024 BA-Hub 比赛记录分析器 | 数据来源: batrace.aoeiaol.top</p>
        </footer>
    </div>

    <script src="api.js"></script>
    <script src="utils.js"></script>
    <script src="match-detail.js"></script>
</body>
</html>
