# BA-Hub 比赛记录分析器 - 部署指南

## 快速开始

### 1. 本地开发环境

#### 使用 Python (推荐)
```bash
# 进入项目目录
cd ba-hub-analyzer

# 启动HTTP服务器
python -m http.server 8000

# 访问网站
# 浏览器打开: http://localhost:8000
```

#### 使用 Node.js
```bash
# 安装http-server (如果没有安装)
npm install -g http-server

# 启动服务器
http-server -p 8000

# 访问网站
# 浏览器打开: http://localhost:8000
```

#### 使用 PHP
```bash
# 启动PHP内置服务器
php -S localhost:8000

# 访问网站
# 浏览器打开: http://localhost:8000
```

### 2. 测试功能

访问测试页面: `http://localhost:8000/test.html`

测试项目包括：
- 本地JSON数据解析
- API连接测试
- 数据解析功能
- 工具函数测试

## 生产环境部署

### 1. 静态网站托管

#### GitHub Pages
1. 将代码推送到GitHub仓库
2. 在仓库设置中启用GitHub Pages
3. 选择源分支（通常是main或gh-pages）
4. 访问 `https://username.github.io/repository-name`

#### Netlify
1. 连接GitHub仓库到Netlify
2. 设置构建命令（无需构建，直接部署）
3. 设置发布目录为根目录 `/`
4. 部署完成后获得自定义域名

#### Vercel
1. 导入GitHub仓库到Vercel
2. 无需配置，自动部署
3. 获得自动生成的域名

### 2. 传统Web服务器

#### Apache
```apache
# .htaccess 文件配置
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 启用GZIP压缩
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
    </IfModule>
    
    # 设置缓存
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
    </IfModule>
</IfModule>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/ba-hub-analyzer;
    index index.html;

    # 启用GZIP压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 设置缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # 处理所有请求
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 环境配置

### CORS 配置

如果遇到CORS问题，需要在服务器端配置：

#### Apache
```apache
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type"
```

#### Nginx
```nginx
add_header Access-Control-Allow-Origin *;
add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
add_header Access-Control-Allow-Headers "Content-Type";
```

### HTTPS 配置

生产环境建议启用HTTPS：

1. 获取SSL证书（Let's Encrypt免费）
2. 配置服务器支持HTTPS
3. 重定向HTTP到HTTPS

## 性能优化

### 1. 文件压缩
- 启用GZIP压缩
- 压缩CSS和JavaScript文件
- 优化图片资源

### 2. 缓存策略
- 设置静态资源缓存
- 使用CDN加速
- 启用浏览器缓存

### 3. 代码优化
- 压缩JavaScript和CSS
- 移除未使用的代码
- 优化图片大小

## 监控和维护

### 1. 错误监控
- 设置错误日志
- 监控API调用失败
- 用户行为分析

### 2. 性能监控
- 页面加载时间
- API响应时间
- 用户体验指标

### 3. 定期维护
- 更新依赖库
- 检查API兼容性
- 备份用户数据

## 故障排除

### 常见问题

1. **CORS错误**
   - 确保服务器配置了正确的CORS头
   - 检查API端点是否支持跨域请求

2. **API连接失败**
   - 检查网络连接
   - 验证API端点是否可用
   - 检查Steam ID格式

3. **数据不显示**
   - 检查数据格式是否正确
   - 验证DOM元素是否存在
   - 检查JavaScript控制台错误

4. **样式问题**
   - 检查CSS文件是否正确加载
   - 验证Font Awesome图标库
   - 检查浏览器兼容性

### 调试工具

1. 浏览器开发者工具
2. 网络面板检查请求
3. 控制台查看错误信息
4. 使用测试页面验证功能

## 安全考虑

1. **输入验证**
   - 验证Steam ID格式
   - 防止XSS攻击
   - 限制API请求频率

2. **数据保护**
   - 不存储敏感信息
   - 使用HTTPS传输
   - 定期清理缓存

3. **访问控制**
   - 设置适当的文件权限
   - 隐藏敏感配置文件
   - 监控异常访问

## 更新和维护

### 版本控制
- 使用Git管理代码
- 创建发布标签
- 维护更新日志

### 自动化部署
- 设置CI/CD流水线
- 自动化测试
- 自动部署到生产环境
