/**
 * 图表模块
 * 负责创建和管理各种数据可视化图表
 */

class ChartManager {
    constructor() {
        this.charts = new Map(); // 存储图表实例
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            }
        };
    }

    /**
     * 创建单位使用频率图表
     * @param {string} canvasId - Canvas元素ID
     * @param {Map} unitsData - 单位使用数据
     */
    createUnitsChart(canvasId, unitsData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas元素 ${canvasId} 不存在`);
            return;
        }

        // 销毁现有图表
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        // 准备数据
        const sortedUnits = Array.from(unitsData.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10); // 只显示前10个最常用的单位

        const labels = sortedUnits.map(([unitId]) => window.baHubAPI.getUnitName(unitId));
        const data = sortedUnits.map(([, count]) => count);
        const colors = Utils.getChartColors(data.length);

        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    label: '使用次数',
                    data: data,
                    backgroundColor: colors,
                    borderColor: colors.map(color => color + '80'),
                    borderWidth: 2
                }]
            },
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '最常用单位 (前10名)'
                    }
                }
            }
        });

        this.charts.set(canvasId, chart);
    }

    /**
     * 创建伤害分布图表
     * @param {string} canvasId - Canvas元素ID
     * @param {Array} matchesData - 比赛数据
     */
    createDamageChart(canvasId, matchesData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas元素 ${canvasId} 不存在`);
            return;
        }

        // 销毁现有图表
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        // 准备数据 - 最近20场比赛的伤害趋势
        const recentMatches = matchesData.slice(0, 20).reverse();
        const labels = recentMatches.map((match, index) => `第${index + 1}场`);
        const damageDealt = recentMatches.map(match => {
            // 假设第一个玩家是查询的玩家
            return match.players[0]?.damageDealt || 0;
        });
        const damageReceived = recentMatches.map(match => {
            return match.players[0]?.damageReceived || 0;
        });

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '造成伤害',
                        data: damageDealt,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '受到伤害',
                        data: damageReceived,
                        borderColor: '#f5576c',
                        backgroundColor: 'rgba(245, 87, 108, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '伤害值'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '比赛场次'
                        }
                    }
                },
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '最近20场比赛伤害趋势'
                    }
                }
            }
        });

        this.charts.set(canvasId, chart);
    }

    /**
     * 创建胜率统计图表
     * @param {string} canvasId - Canvas元素ID
     * @param {Object} statsData - 统计数据
     */
    createWinRateChart(canvasId, statsData) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas元素 ${canvasId} 不存在`);
            return;
        }

        // 销毁现有图表
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
        }

        const data = [
            statsData.totalWins,
            statsData.totalLosses,
            statsData.totalDraws
        ];

        const chart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['胜利', '失败', '平局'],
                datasets: [{
                    data: data,
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107'],
                    borderColor: ['#1e7e34', '#bd2130', '#d39e00'],
                    borderWidth: 2
                }]
            },
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    title: {
                        display: true,
                        text: '比赛结果分布'
                    }
                }
            }
        });

        this.charts.set(canvasId, chart);
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        this.charts.forEach(chart => {
            chart.destroy();
        });
        this.charts.clear();
    }

    /**
     * 销毁指定图表
     * @param {string} canvasId - Canvas元素ID
     */
    destroyChart(canvasId) {
        if (this.charts.has(canvasId)) {
            this.charts.get(canvasId).destroy();
            this.charts.delete(canvasId);
        }
    }
}

// 创建全局图表管理器实例
window.chartManager = new ChartManager();
