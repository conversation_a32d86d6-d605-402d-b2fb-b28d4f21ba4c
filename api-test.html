<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API实时测试 - 检查最新比赛</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .match-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .match-item.highlight {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>API实时测试 - 检查最新比赛</h1>
        
        <div class="info">
            <p>这个工具用于实时测试BA-Hub API，检查是否能获取到最新的比赛记录。</p>
            <p><strong>目标比赛：</strong></p>
            <ul>
                <li>比赛ID: 3338039 (预期时间: 2025/9/14 23:56:38)</li>
                <li>比赛ID: 3334241 (预期时间: 2025/9/14 20:31:43)</li>
            </ul>
        </div>

        <div class="controls">
            <button class="test-button" onclick="testAPI()">测试实时API</button>
            <button class="test-button" onclick="testLocalData()">测试本地数据</button>
            <button class="test-button" onclick="compareData()">对比数据</button>
        </div>

        <div id="results" class="results" style="display: none;">
            <!-- 测试结果将显示在这里 -->
        </div>
    </div>

    <script src="api.js"></script>
    <script>
        let apiData = null;
        let localData = null;

        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<div class="loading">正在测试实时API...</div>';

            try {
                const steamId = '76561198017063877';
                const rawData = await window.baHubAPI.getMatchesBySteamId(steamId);
                apiData = window.baHubAPI.parseMatchData(rawData);
                
                displayAPIResults(apiData, 'API实时数据');
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">API测试失败: ${error.message}</div>`;
            }
        }

        async function testLocalData() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<div class="loading">正在加载本地数据...</div>';

            try {
                const response = await fetch('1.json');
                const rawData = await response.json();
                localData = window.baHubAPI.parseMatchData(rawData);
                
                displayAPIResults(localData, '本地示例数据');
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">本地数据加载失败: ${error.message}</div>`;
            }
        }

        function displayAPIResults(data, title) {
            const resultsDiv = document.getElementById('results');
            
            const matches = data.matches.slice(0, 10); // 显示前10场比赛
            const targetMatches = ['3338039', '3334241'];
            
            let html = `<h3>${title}</h3>`;
            
            // 统计信息
            html += `<div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${data.matches.length}</div>
                    <div class="stat-label">总比赛数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${matches[0] ? new Date(matches[0].endTime * 1000).toLocaleString('zh-CN') : 'N/A'}</div>
                    <div class="stat-label">最新比赛时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${matches[0] ? matches[0].fightId : 'N/A'}</div>
                    <div class="stat-label">最新比赛ID</div>
                </div>
            </div>`;

            // 检查目标比赛
            html += '<h4>目标比赛检查：</h4>';
            targetMatches.forEach(targetId => {
                const found = data.matches.find(m => m.fightId === targetId);
                if (found) {
                    const date = new Date(found.endTime * 1000).toLocaleString('zh-CN');
                    html += `<div class="match-item highlight">
                        ✅ 找到目标比赛 ${targetId} - 时间: ${date}
                    </div>`;
                } else {
                    html += `<div class="match-item">
                        ❌ 未找到目标比赛 ${targetId}
                    </div>`;
                }
            });

            // 最新比赛列表
            html += '<h4>最新10场比赛：</h4>';
            matches.forEach((match, index) => {
                const date = new Date(match.endTime * 1000).toLocaleString('zh-CN');
                const isTarget = targetMatches.includes(match.fightId);
                html += `<div class="match-item ${isTarget ? 'highlight' : ''}">
                    <strong>${index + 1}. 比赛 #${match.fightId}</strong> ${isTarget ? '🎯' : ''}<br>
                    时间: ${date}<br>
                    结果: ${match.result}<br>
                    玩家数: ${match.players.length}
                </div>`;
            });

            resultsDiv.innerHTML = html;
        }

        function compareData() {
            if (!apiData || !localData) {
                alert('请先测试API和本地数据');
                return;
            }

            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            
            let html = '<h3>数据对比结果</h3>';
            
            html += `<div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${apiData.matches.length}</div>
                    <div class="stat-label">API比赛数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${localData.matches.length}</div>
                    <div class="stat-label">本地比赛数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${apiData.matches.length - localData.matches.length}</div>
                    <div class="stat-label">差异</div>
                </div>
            </div>`;

            // 检查最新比赛
            const apiLatest = apiData.matches[0];
            const localLatest = localData.matches[0];
            
            html += '<h4>最新比赛对比：</h4>';
            html += `<div class="code">
API最新: ${apiLatest ? `${apiLatest.fightId} (${new Date(apiLatest.endTime * 1000).toLocaleString('zh-CN')})` : 'N/A'}<br>
本地最新: ${localLatest ? `${localLatest.fightId} (${new Date(localLatest.endTime * 1000).toLocaleString('zh-CN')})` : 'N/A'}
            </div>`;

            // 查找API中有但本地没有的比赛
            const localIds = new Set(localData.matches.map(m => m.fightId));
            const newMatches = apiData.matches.filter(m => !localIds.has(m.fightId));
            
            if (newMatches.length > 0) {
                html += `<h4>API中的新比赛 (${newMatches.length}场)：</h4>`;
                newMatches.slice(0, 5).forEach(match => {
                    const date = new Date(match.endTime * 1000).toLocaleString('zh-CN');
                    html += `<div class="match-item highlight">
                        新比赛 #${match.fightId} - ${date}
                    </div>`;
                });
            } else {
                html += '<div class="match-item">API和本地数据相同，没有新比赛</div>';
            }

            resultsDiv.innerHTML = html;
        }

        // 页面加载时的提示
        document.addEventListener('DOMContentLoaded', () => {
            console.log('API测试页面已加载');
            console.log('点击按钮开始测试...');
        });
    </script>
</body>
</html>
