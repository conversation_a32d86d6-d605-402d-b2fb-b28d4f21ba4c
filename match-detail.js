/**
 * 比赛详情页面应用程序
 * 负责显示单场比赛的详细信息
 */

class MatchDetailApp {
    constructor() {
        this.matchData = null;
        this.currentPlayerTab = 0;
        this.urlParams = new URLSearchParams(window.location.search);
        
        this.initializeApp();
    }

    /**
     * 初始化应用程序
     */
    async initializeApp() {
        const fightId = this.urlParams.get('fightId');
        const steamId = this.urlParams.get('steamId');

        if (!fightId || !steamId) {
            this.showError('缺少必要的参数：比赛ID或Steam ID');
            return;
        }

        try {
            await this.loadMatchData(steamId, fightId);
        } catch (error) {
            console.error('加载比赛数据失败:', error);
            this.showError(error.message);
        }
    }

    /**
     * 加载比赛数据
     * @param {string} steamId - Steam ID
     * @param {string} fightId - 比赛ID
     */
    async loadMatchData(steamId, fightId) {
        try {
            // 获取玩家的所有比赛数据
            const rawData = await window.baHubAPI.getMatchesBySteamId(steamId);
            const parsedData = window.baHubAPI.parseMatchData(rawData);

            // 查找指定的比赛
            const match = parsedData.matches.find(m => m.fightId === fightId);
            if (!match) {
                throw new Error('未找到指定的比赛记录');
            }

            this.matchData = match;
            this.displayMatchDetail();

        } catch (error) {
            throw new Error(`加载比赛数据失败: ${error.message}`);
        }
    }

    /**
     * 显示比赛详情
     */
    displayMatchDetail() {
        if (!this.matchData) return;

        // 隐藏加载状态，显示内容
        document.getElementById('loadingSection').style.display = 'none';
        document.getElementById('matchDetailContent').style.display = 'block';

        // 显示各个部分
        this.displayMatchHeader();
        this.displayMatchBasicInfo();
        this.displayPlayersComparison();
        this.displayUnitsAnalysis();
        this.displayMatchStatistics();
    }

    /**
     * 显示比赛头部信息
     */
    displayMatchHeader() {
        const matchInfoHeader = document.getElementById('matchInfoHeader');
        if (!matchInfoHeader) return;

        const result = Utils.formatMatchResult(this.matchData.result);
        const date = Utils.formatDate(this.matchData.endTime);

        matchInfoHeader.innerHTML = `
            <div class="match-header-info">
                <span class="match-id">比赛 #${this.matchData.fightId}</span>
                <span class="match-result ${result.class}">${result.text}</span>
                <span class="match-date">${date}</span>
            </div>
        `;
    }

    /**
     * 显示比赛基本信息
     */
    displayMatchBasicInfo() {
        const matchBasicInfo = document.getElementById('matchBasicInfo');
        if (!matchBasicInfo) return;

        const duration = Utils.formatDuration(this.matchData.totalPlayTime);
        const mapName = window.baHubAPI.getMapName(this.matchData.mapId);

        matchBasicInfo.innerHTML = `
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">比赛ID:</span>
                    <span class="info-value">${this.matchData.fightId}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">比赛结果:</span>
                    <span class="info-value ${Utils.formatMatchResult(this.matchData.result).class}">
                        ${Utils.formatMatchResult(this.matchData.result).text}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">比赛时长:</span>
                    <span class="info-value">${duration}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">地图:</span>
                    <span class="info-value">${mapName}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">结束时间:</span>
                    <span class="info-value">${Utils.formatDate(this.matchData.endTime)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">参与玩家:</span>
                    <span class="info-value">${this.matchData.players.length} 人</span>
                </div>
            </div>
        `;
    }

    /**
     * 显示玩家表现对比
     */
    displayPlayersComparison() {
        const playersGrid = document.getElementById('playersGrid');
        if (!playersGrid) return;

        playersGrid.innerHTML = this.matchData.players.map(player => `
            <div class="player-card">
                <div class="player-header">
                    <h3 class="player-name">${player.name}</h3>
                    <div class="player-id">ID: ${player.id}</div>
                </div>
                <div class="player-stats">
                    <div class="stat-row">
                        <span class="stat-label">伤害比率:</span>
                        <span class="stat-value">${player.dlRatio.toFixed(2)}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">造成伤害:</span>
                        <span class="stat-value">${Utils.formatNumber(player.damageDealt)}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">受到伤害:</span>
                        <span class="stat-value">${Utils.formatNumber(player.damageReceived)}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">摧毁数:</span>
                        <span class="stat-value">${player.destruction}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">使用单位:</span>
                        <span class="stat-value">${player.units.length}</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">奖章:</span>
                        <span class="stat-value">${player.medals.length}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 显示单位分析
     */
    displayUnitsAnalysis() {
        this.displayPlayerTabs();
        this.displayUnitsContent(0); // 默认显示第一个玩家
    }

    /**
     * 显示玩家标签页
     */
    displayPlayerTabs() {
        const playersTabs = document.getElementById('playersTabs');
        if (!playersTabs) return;

        playersTabs.innerHTML = this.matchData.players.map((player, index) => `
            <button class="player-tab ${index === 0 ? 'active' : ''}" 
                    onclick="matchDetailApp.switchPlayerTab(${index})">
                ${player.name}
            </button>
        `).join('');
    }

    /**
     * 切换玩家标签页
     * @param {number} playerIndex - 玩家索引
     */
    switchPlayerTab(playerIndex) {
        this.currentPlayerTab = playerIndex;
        
        // 更新标签页样式
        document.querySelectorAll('.player-tab').forEach((tab, index) => {
            tab.classList.toggle('active', index === playerIndex);
        });

        // 显示对应玩家的单位内容
        this.displayUnitsContent(playerIndex);
    }

    /**
     * 显示单位内容
     * @param {number} playerIndex - 玩家索引
     */
    displayUnitsContent(playerIndex) {
        const unitsContent = document.getElementById('unitsContent');
        if (!unitsContent || !this.matchData.players[playerIndex]) return;

        const player = this.matchData.players[playerIndex];
        const units = player.units.filter(unit => !unit.wasRefunded); // 过滤掉退款的单位

        if (units.length === 0) {
            unitsContent.innerHTML = '<p class="no-data">该玩家没有使用单位数据</p>';
            return;
        }

        // 按伤害排序
        const sortedUnits = units.sort((a, b) => (b.damageDealt || 0) - (a.damageDealt || 0));

        unitsContent.innerHTML = `
            <div class="units-summary">
                <h3>${player.name} 的单位使用情况</h3>
                <p>共使用 ${units.length} 个单位</p>
            </div>
            <div class="units-table">
                <div class="table-header">
                    <div class="col-unit">单位名称</div>
                    <div class="col-damage">造成伤害</div>
                    <div class="col-received">受到伤害</div>
                    <div class="col-kills">击杀数</div>
                    <div class="col-supply">补给消耗</div>
                </div>
                ${sortedUnits.map(unit => `
                    <div class="table-row">
                        <div class="col-unit">
                            <span class="unit-name">${window.baHubAPI.getUnitName(unit.unitId)}</span>
                            <span class="unit-id">ID: ${unit.unitId}</span>
                        </div>
                        <div class="col-damage">${Utils.formatNumber(unit.damageDealt || 0)}</div>
                        <div class="col-received">${Utils.formatNumber(unit.damageReceived || 0)}</div>
                        <div class="col-kills">${unit.killedCount || 0}</div>
                        <div class="col-supply">${Utils.formatNumber(unit.supplyConsumed || 0)}</div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 显示比赛统计
     */
    displayMatchStatistics() {
        const statsGrid = document.getElementById('statsGrid');
        if (!statsGrid) return;

        // 计算统计数据
        const totalDamageDealt = this.matchData.players.reduce((sum, p) => sum + (p.damageDealt || 0), 0);
        const totalDamageReceived = this.matchData.players.reduce((sum, p) => sum + (p.damageReceived || 0), 0);
        const totalDestruction = this.matchData.players.reduce((sum, p) => sum + (p.destruction || 0), 0);
        const totalUnits = this.matchData.players.reduce((sum, p) => sum + p.units.length, 0);

        const topDamagePlayer = this.matchData.players.reduce((max, p) => 
            (p.damageDealt || 0) > (max.damageDealt || 0) ? p : max
        );

        const topDestructionPlayer = this.matchData.players.reduce((max, p) => 
            (p.destruction || 0) > (max.destruction || 0) ? p : max
        );

        statsGrid.innerHTML = `
            <div class="stat-card">
                <div class="stat-title">总造成伤害</div>
                <div class="stat-number">${Utils.formatNumber(totalDamageDealt)}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">总受到伤害</div>
                <div class="stat-number">${Utils.formatNumber(totalDamageReceived)}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">总摧毁数</div>
                <div class="stat-number">${totalDestruction}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">总使用单位</div>
                <div class="stat-number">${totalUnits}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">最高伤害玩家</div>
                <div class="stat-number">${topDamagePlayer.name}</div>
                <div class="stat-detail">${Utils.formatNumber(topDamagePlayer.damageDealt)}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">最高摧毁玩家</div>
                <div class="stat-number">${topDestructionPlayer.name}</div>
                <div class="stat-detail">${topDestructionPlayer.destruction}</div>
            </div>
        `;
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        document.getElementById('loadingSection').style.display = 'none';
        document.getElementById('errorSection').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.matchDetailApp = new MatchDetailApp();
    console.log('比赛详情页面已初始化');
});
