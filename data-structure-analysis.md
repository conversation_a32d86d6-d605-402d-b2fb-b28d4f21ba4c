# BA-Hub 比赛记录数据结构分析

## 数据概览
API返回的数据是一个数组，包含以下类型的对象：

### 1. 元数据对象 (type: "metadata")
```json
{
  "processedMatches": 0,
  "steamId": "76561198017063877", 
  "totalMatches": 100,
  "type": "metadata"
}
```

### 2. 比赛数据对象 (type: "match")
```json
{
  "data": {
    "Data": {
      "玩家ID": {
        "DLRatio": 数值,
        "DamageDealt": 总伤害,
        "DamageReceived": 受到伤害,
        "Destruction": 摧毁数,
        "Id": 玩家ID,
        "Name": "玩家名称",
        "UnitData": {
          "单位实例ID": {
            "Id": 单位类型ID,
            "OptionIds": [升级选项],
            "TotalDamageDealt": 造成伤害,
            "TotalDamageReceived": 受到伤害,
            "KilledCount": 击杀数,
            "WasRefunded": 是否退款
          }
        }
      }
    },
    "EndTime": 结束时间戳,
    "MapId": 地图ID,
    "result": "胜负结果",
    "fightId": "比赛ID"
  },
  "type": "match"
}
```

## 关键字段说明

### 玩家数据字段
- **DLRatio**: 伤害/受伤比率
- **DamageDealt**: 总造成伤害
- **DamageReceived**: 总受到伤害  
- **Destruction**: 摧毁建筑/单位数量
- **Medals**: 获得的奖章
- **SupplyPointsConsumed**: 消耗的补给点数
- **UnitData**: 使用的单位详细数据

### 单位数据字段
- **Id**: 单位类型ID
- **OptionIds**: 单位的升级选项
- **TotalDamageDealt**: 该单位造成的总伤害
- **TotalDamageReceived**: 该单位受到的总伤害
- **KilledCount**: 该单位的击杀数
- **TotalSupplyPointsConsumed**: 该单位消耗的补给点
- **WasRefunded**: 是否被退款（可能表示单位被回收）

### 比赛信息字段
- **EndTime**: 比赛结束时间戳
- **MapId**: 地图ID
- **result**: 比赛结果 ("draw", "victory", "defeat")
- **fightId**: 唯一的比赛ID
- **TotalPlayTimeInSec**: 比赛总时长（秒）

## 数据分析要点

1. **玩家表现分析**：可以通过DLRatio、总伤害、击杀数等指标评估玩家表现
2. **单位使用分析**：通过UnitData可以分析每个玩家最常用的单位、单位效率等
3. **比赛趋势分析**：通过时间戳可以分析玩家的进步趋势
4. **地图偏好分析**：通过MapId可以分析玩家在不同地图上的表现
