<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比赛详情页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .info-box {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>比赛详情页面测试</h1>
        
        <div class="info-box">
            <h2>测试说明</h2>
            <p>这个页面提供了一些测试链接，用于验证比赛详情页面的功能。</p>
            <p>点击下面的链接将打开比赛详情页面，显示指定比赛的详细信息。</p>
        </div>

        <h2>测试链接</h2>
        
        <h3>示例比赛 1</h3>
        <div class="code">
            比赛ID: 3326054<br>
            Steam ID: 76561198017063877
        </div>
        <a href="match-detail.html?fightId=3326054&steamId=76561198017063877" 
           class="test-link" target="_blank">
            查看比赛详情 #3326054
        </a>

        <h3>URL 参数说明</h3>
        <div class="info-box">
            <p>比赛详情页面需要以下URL参数：</p>
            <ul>
                <li><strong>fightId</strong>: 比赛ID（从比赛数据中获取）</li>
                <li><strong>steamId</strong>: Steam ID（用于获取比赛数据）</li>
            </ul>
            
            <p>URL格式示例：</p>
            <div class="code">
                match-detail.html?fightId=3326054&steamId=76561198017063877
            </div>
        </div>

        <h3>功能测试清单</h3>
        <div class="info-box">
            <h4>页面加载测试</h4>
            <ul>
                <li>✓ 检查页面是否正常加载</li>
                <li>✓ 验证加载动画显示</li>
                <li>✓ 确认数据获取成功</li>
            </ul>

            <h4>数据显示测试</h4>
            <ul>
                <li>✓ 比赛基本信息显示正确</li>
                <li>✓ 队伍对战数据完整（两队各5人）</li>
                <li>✓ 玩家数据按队伍正确分组</li>
                <li>✓ 单位分析数据准确</li>
                <li>✓ 队伍级别统计数据计算正确</li>
            </ul>

            <h4>交互功能测试</h4>
            <ul>
                <li>✓ 玩家标签页切换正常</li>
                <li>✓ 返回主页链接有效</li>
                <li>✓ 响应式设计适配</li>
            </ul>
        </div>

        <h3>错误处理测试</h3>
        <p>测试各种错误情况的处理：</p>
        
        <a href="match-detail.html" class="test-link" target="_blank">
            测试缺少参数
        </a>
        
        <a href="match-detail.html?fightId=invalid&steamId=76561198017063877" 
           class="test-link" target="_blank">
            测试无效比赛ID
        </a>
        
        <a href="match-detail.html?fightId=3326054&steamId=invalid" 
           class="test-link" target="_blank">
            测试无效Steam ID
        </a>

        <h3>开发工具</h3>
        <div class="info-box">
            <p>在测试过程中，建议使用浏览器开发者工具：</p>
            <ul>
                <li><strong>控制台</strong>: 查看JavaScript错误和日志</li>
                <li><strong>网络</strong>: 监控API请求和响应</li>
                <li><strong>元素</strong>: 检查DOM结构和样式</li>
                <li><strong>应用</strong>: 查看本地存储数据</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="index.html" class="test-link">返回主页</a>
            <a href="test.html" class="test-link">API测试页面</a>
        </div>
    </div>

    <script>
        // 简单的页面功能测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('比赛详情测试页面已加载');
            
            // 检查是否有本地服务器
            if (location.protocol === 'file:') {
                const warning = document.createElement('div');
                warning.style.cssText = `
                    background: #fff3cd;
                    color: #856404;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 20px 0;
                    border: 1px solid #ffeaa7;
                `;
                warning.innerHTML = `
                    <strong>注意:</strong> 检测到您正在使用文件协议访问页面。
                    为了正常测试API功能，建议使用本地HTTP服务器。
                `;
                document.querySelector('.test-container').insertBefore(
                    warning, 
                    document.querySelector('h2')
                );
            }
        });
    </script>
</body>
</html>
