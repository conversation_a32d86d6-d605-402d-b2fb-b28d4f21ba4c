/**
 * 主应用程序
 * 负责协调各个模块，处理用户交互
 */

class BaHubApp {
    constructor() {
        this.currentData = null;
        this.currentSteamId = null;
        this.isLoading = false;
        
        this.initializeEventListeners();
        this.loadSavedSteamId();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 搜索表单提交
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }

        // Steam ID 输入框回车键
        const steamIdInput = document.getElementById('steamIdInput');
        if (steamIdInput) {
            steamIdInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });
        }


    }

    /**
     * 加载保存的Steam ID
     */
    loadSavedSteamId() {
        const savedSteamId = Utils.storage.get('lastSteamId');
        if (savedSteamId) {
            const steamIdInput = document.getElementById('steamIdInput');
            if (steamIdInput) {
                steamIdInput.value = savedSteamId;
            }
        }
    }

    /**
     * 处理搜索请求
     */
    async handleSearch() {
        if (this.isLoading) return;

        const steamIdInput = document.getElementById('steamIdInput');
        const steamId = steamIdInput.value.trim();

        if (!steamId) {
            this.showError('请输入Steam ID');
            return;
        }

        if (!window.baHubAPI.isValidSteamId(steamId)) {
            this.showError('Steam ID格式不正确，请输入17位数字的Steam64 ID');
            return;
        }

        this.setLoading(true);
        this.hideError();

        try {
            // 保存Steam ID
            Utils.storage.set('lastSteamId', steamId);
            this.currentSteamId = steamId;

            // 获取数据
            const rawData = await window.baHubAPI.getMatchesBySteamId(steamId);
            this.currentData = window.baHubAPI.parseMatchData(rawData);

            // 显示结果
            this.displayResults();
            Utils.showNotification('数据加载成功！', 'success');

        } catch (error) {
            console.error('搜索失败:', error);
            this.showError(error.message);
            Utils.showNotification('数据加载失败', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * 显示结果
     */
    displayResults() {
        if (!this.currentData) return;

        // 显示结果区域
        const resultsSection = document.getElementById('resultsSection');
        if (resultsSection) {
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示玩家概览
        this.displayPlayerOverview();

        // 显示比赛列表
        this.displayMatches();

        // 显示单位分析
        this.displayUnitsAnalysis();
    }

    /**
     * 显示玩家概览
     */
    displayPlayerOverview() {
        const overviewStats = document.getElementById('overviewStats');
        if (!overviewStats || !this.currentData) return;

        const stats = this.currentData.playerStats;
        const winRate = Utils.calculateWinRate(stats.totalWins, stats.totalMatches);

        overviewStats.innerHTML = `
            <div class="stat-card">
                <div class="stat-value">${Utils.formatNumber(stats.totalMatches)}</div>
                <div class="stat-label">总比赛数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${Utils.formatPercentage(winRate)}</div>
                <div class="stat-label">胜率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${Utils.formatNumber(stats.totalDamageDealt)}</div>
                <div class="stat-label">总造成伤害</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${stats.averageDLRatio.toFixed(2)}</div>
                <div class="stat-label">平均伤害比</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${stats.totalWins}</div>
                <div class="stat-label">胜利次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${stats.totalLosses}</div>
                <div class="stat-label">失败次数</div>
            </div>
        `;
    }

    /**
     * 显示比赛列表
     */
    displayMatches() {
        const matchesContainer = document.getElementById('matchesContainer');
        if (!matchesContainer || !this.currentData) return;

        // 调试信息：显示排序状态
        console.log('比赛数据排序检查:');
        console.log('总比赛数:', this.currentData.matches.length);
        if (this.currentData.matches.length > 0) {
            console.log('最新比赛:', this.currentData.matches[0].fightId,
                       new Date(this.currentData.matches[0].endTime * 1000).toLocaleString('zh-CN'));
            if (this.currentData.matches[1]) {
                console.log('第二新:', this.currentData.matches[1].fightId,
                           new Date(this.currentData.matches[1].endTime * 1000).toLocaleString('zh-CN'));
            }
        }

        const matches = this.currentData.matches.slice(0, 20); // 只显示最近20场

        matchesContainer.innerHTML = matches.map(match => {
            const result = Utils.formatMatchResult(match.result);
            const date = Utils.formatDate(match.endTime);
            const duration = Utils.formatDuration(match.totalPlayTime);
            const player = match.players[0]; // 假设第一个是查询的玩家

            return `
                <div class="match-item" onclick="app.goToMatchDetail('${match.fightId}')">
                    <div class="match-header">
                        <div>
                            <strong>比赛 #${match.fightId}</strong>
                            <span class="match-result ${result.class}">${result.text}</span>
                        </div>
                        <div class="match-date">${date}</div>
                    </div>
                    <div class="match-stats">
                        <span>伤害: ${Utils.formatNumber(player?.damageDealt || 0)}</span>
                        <span>时长: ${duration}</span>
                        <span>地图: ${window.baHubAPI.getMapName(match.mapId)}</span>
                        <span>单位数: ${player?.units?.length || 0}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 显示单位分析
     */
    displayUnitsAnalysis() {
        const unitsAnalysis = document.getElementById('unitsAnalysis');
        if (!unitsAnalysis || !this.currentData) return;

        const stats = this.currentData.playerStats;

        // 获取最常用的单位（前10个）
        const topUnits = Array.from(stats.favoriteUnits.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10);

        if (topUnits.length === 0) {
            unitsAnalysis.innerHTML = '<p>暂无单位使用数据</p>';
            return;
        }

        // 计算总使用次数
        const totalUsage = topUnits.reduce((sum, [, count]) => sum + count, 0);

        unitsAnalysis.innerHTML = `
            <div class="analysis-summary">
                <h3>单位使用统计</h3>
                <p>共使用了 ${stats.favoriteUnits.size} 种不同单位，总计 ${totalUsage} 次</p>
            </div>
            <div class="unit-stats-grid">
                ${topUnits.map(([unitId, count]) => {
                    const percentage = ((count / totalUsage) * 100).toFixed(1);
                    return `
                        <div class="unit-stat-card">
                            <div class="unit-name">${window.baHubAPI.getUnitName(unitId)}</div>
                            <div class="unit-details">
                                <div class="unit-detail">
                                    <span>使用次数:</span>
                                    <span class="unit-detail-value">${count}</span>
                                </div>
                                <div class="unit-detail">
                                    <span>使用率:</span>
                                    <span class="unit-detail-value">${percentage}%</span>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    /**
     * 跳转到比赛详情页面
     * @param {string} fightId - 比赛ID
     */
    goToMatchDetail(fightId) {
        if (!this.currentSteamId) {
            Utils.showNotification('无法获取Steam ID', 'error');
            return;
        }

        // 跳转到比赛详情页面
        const url = `match-detail.html?fightId=${fightId}&steamId=${this.currentSteamId}`;
        window.open(url, '_blank');
    }



    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
        this.isLoading = loading;
        const searchBtn = document.getElementById('searchBtn');
        const btnText = searchBtn?.querySelector('.btn-text');
        const loadingSpinner = searchBtn?.querySelector('.loading-spinner');

        if (searchBtn) {
            searchBtn.disabled = loading;
        }

        if (btnText && loadingSpinner) {
            if (loading) {
                btnText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
            } else {
                btnText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
            }
        }
    }

    /**
     * 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }
    }

    /**
     * 隐藏错误消息
     */
    hideError() {
        const errorMessage = document.getElementById('errorMessage');
        if (errorMessage) {
            errorMessage.style.display = 'none';
        }
    }
}

/**
 * 填充示例Steam ID
 * @param {string} steamId - Steam ID
 */
function fillExample(steamId) {
    const steamIdInput = document.getElementById('steamIdInput');
    if (steamIdInput) {
        steamIdInput.value = steamId;
        steamIdInput.focus();
    }
}



// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new BaHubApp();
    console.log('BA-Hub 应用已初始化');
});
