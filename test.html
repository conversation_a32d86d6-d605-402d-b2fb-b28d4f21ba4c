<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BA-Hub API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .info { border-left: 4px solid #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>BA-Hub API 测试页面</h1>
        
        <div class="test-section">
            <h2>1. 测试本地JSON数据解析</h2>
            <p>测试解析本地的 1.json 文件</p>
            <button onclick="testLocalData()">测试本地数据</button>
            <div id="localDataResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>2. 测试API连接</h2>
            <p>测试与 BA-Hub API 的连接</p>
            <input type="text" id="testSteamId" placeholder="输入Steam ID" value="76561198017063877" style="padding: 8px; margin-right: 10px; width: 200px;">
            <button onclick="testAPIConnection()">测试API连接</button>
            <div id="apiResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>3. 测试数据解析功能</h2>
            <p>测试数据解析和统计功能</p>
            <button onclick="testDataParsing()">测试数据解析</button>
            <div id="parseResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>4. 测试工具函数</h2>
            <p>测试各种工具函数</p>
            <button onclick="testUtilFunctions()">测试工具函数</button>
            <div id="utilResult" class="test-result"></div>
        </div>
    </div>

    <script src="api.js"></script>
    <script src="utils.js"></script>
    <script>
        // 测试本地JSON数据
        async function testLocalData() {
            const resultDiv = document.getElementById('localDataResult');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在加载本地数据...';

            try {
                const response = await fetch('1.json');
                const data = await response.json();
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `本地数据加载成功！
数据类型: ${Array.isArray(data) ? '数组' : typeof data}
数据长度: ${data.length}
第一个元素类型: ${data[0]?.type}
第二个元素类型: ${data[1]?.type}

数据预览:
${JSON.stringify(data.slice(0, 2), null, 2)}`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `本地数据加载失败: ${error.message}`;
            }
        }

        // 测试API连接
        async function testAPIConnection() {
            const steamId = document.getElementById('testSteamId').value;
            const resultDiv = document.getElementById('apiResult');
            
            if (!steamId) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '请输入Steam ID';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在连接API...';

            try {
                const data = await window.baHubAPI.getMatchesBySteamId(steamId);
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `API连接成功！
数据类型: ${Array.isArray(data) ? '数组' : typeof data}
数据长度: ${data.length}
Steam ID: ${steamId}

数据概览:
${data.map(item => `- ${item.type}: ${item.type === 'metadata' ? `${item.totalMatches} 场比赛` : '比赛数据'}`).join('\n')}`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `API连接失败: ${error.message}`;
            }
        }

        // 测试数据解析
        async function testDataParsing() {
            const resultDiv = document.getElementById('parseResult');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在测试数据解析...';

            try {
                // 使用本地数据进行测试
                const response = await fetch('1.json');
                const rawData = await response.json();
                
                const parsedData = window.baHubAPI.parseMatchData(rawData);
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `数据解析成功！

元数据:
- Steam ID: ${parsedData.metadata?.steamId}
- 总比赛数: ${parsedData.metadata?.totalMatches}

统计数据:
- 解析的比赛数: ${parsedData.matches.length}
- 总胜利: ${parsedData.playerStats.totalWins}
- 总失败: ${parsedData.playerStats.totalLosses}
- 总平局: ${parsedData.playerStats.totalDraws}
- 总造成伤害: ${parsedData.playerStats.totalDamageDealt}
- 平均伤害比: ${parsedData.playerStats.averageDLRatio.toFixed(2)}
- 常用单位数: ${parsedData.playerStats.favoriteUnits.size}

最近一场比赛:
- 比赛ID: ${parsedData.matches[0]?.fightId}
- 结果: ${parsedData.matches[0]?.result}
- 玩家数: ${parsedData.matches[0]?.players.length}`;

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `数据解析失败: ${error.message}`;
            }
        }

        // 测试工具函数
        function testUtilFunctions() {
            const resultDiv = document.getElementById('utilResult');
            
            try {
                const testTimestamp = 1757800189;
                const testNumber = 1234567;
                const testRatio = 0.756;
                
                const tests = [
                    `时间格式化: ${Utils.formatDate(testTimestamp)}`,
                    `数字格式化: ${Utils.formatNumber(testNumber)}`,
                    `百分比格式化: ${Utils.formatPercentage(testRatio)}`,
                    `时长格式化: ${Utils.formatDuration(3665)}`,
                    `比赛结果格式化: ${JSON.stringify(Utils.formatMatchResult('victory'))}`,
                    `胜率计算: ${Utils.calculateWinRate(15, 20)}`,
                    `Steam ID验证: ${window.baHubAPI.isValidSteamId('76561198017063877')}`,
                    `无效Steam ID验证: ${window.baHubAPI.isValidSteamId('123456')}`
                ];
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `工具函数测试成功！

${tests.join('\n')}

本地存储测试:`;
                
                // 测试本地存储
                Utils.storage.set('test', { value: 'test data' });
                const stored = Utils.storage.get('test');
                resultDiv.textContent += `\n- 存储测试: ${JSON.stringify(stored)}`;
                Utils.storage.remove('test');
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `工具函数测试失败: ${error.message}`;
            }
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', () => {
            console.log('BA-Hub 测试页面已加载');
        });
    </script>
</body>
</html>
