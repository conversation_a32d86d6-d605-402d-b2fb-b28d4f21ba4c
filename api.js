/**
 * API 调用模块
 * 负责与 BA-Hub API 进行通信
 */

class BaHubAPI {
    constructor() {
        this.baseURL = 'https://batrace.aoeiaol.top/api/v1';
        this.cache = new Map(); // 简单的缓存机制
    }

    /**
     * 根据 Steam ID 获取比赛记录
     * @param {string} steamId - Steam ID
     * @returns {Promise<Object>} 比赛记录数据
     */
    async getMatchesBySteamId(steamId) {
        if (!steamId || !this.isValidSteamId(steamId)) {
            throw new Error('无效的 Steam ID');
        }

        // 检查缓存
        const cacheKey = `matches_${steamId}`;
        if (this.cache.has(cacheKey)) {
            console.log('从缓存获取数据');
            return this.cache.get(cacheKey);
        }

        try {
            const url = `${this.baseURL}/matches_steam64?steam64=${steamId}`;
            console.log('请求URL:', url);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                },
                // 添加超时处理
                signal: AbortSignal.timeout(30000) // 30秒超时
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} - ${response.statusText}`);
            }

            const data = await response.json();
            
            // 验证数据格式
            if (!Array.isArray(data)) {
                throw new Error('API返回的数据格式不正确');
            }

            // 缓存数据（5分钟过期）
            this.cache.set(cacheKey, data);
            setTimeout(() => {
                this.cache.delete(cacheKey);
            }, 5 * 60 * 1000);

            return data;

        } catch (error) {
            console.error('API请求失败:', error);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            } else if (error.name === 'TypeError') {
                throw new Error('网络连接失败，请检查网络设置');
            } else {
                throw new Error(`获取数据失败: ${error.message}`);
            }
        }
    }

    /**
     * 验证 Steam ID 格式
     * @param {string} steamId - Steam ID
     * @returns {boolean} 是否有效
     */
    isValidSteamId(steamId) {
        // Steam64 ID 通常是17位数字，以7656119开头
        const steamIdRegex = /^7656119\d{10}$/;
        return steamIdRegex.test(steamId);
    }

    /**
     * 解析比赛数据
     * @param {Array} rawData - 原始API数据
     * @returns {Object} 解析后的数据
     */
    parseMatchData(rawData) {
        const result = {
            metadata: null,
            matches: [],
            playerStats: {
                totalMatches: 0,
                totalWins: 0,
                totalLosses: 0,
                totalDraws: 0,
                totalDamageDealt: 0,
                totalDamageReceived: 0,
                averageDLRatio: 0,
                favoriteUnits: new Map(),
                mapStats: new Map()
            }
        };

        for (const item of rawData) {
            if (item.type === 'metadata') {
                result.metadata = item;
                result.playerStats.totalMatches = item.totalMatches || 0;
            } else if (item.type === 'match') {
                const matchData = this.parseMatchItem(item);
                if (matchData) {
                    result.matches.push(matchData);
                    this.updatePlayerStats(result.playerStats, matchData);
                }
            }
        }

        // 计算平均值
        if (result.matches.length > 0) {
            result.playerStats.averageDLRatio = 
                result.playerStats.totalDamageDealt / result.playerStats.totalDamageReceived;
        }

        return result;
    }

    /**
     * 解析单场比赛数据
     * @param {Object} matchItem - 比赛数据项
     * @returns {Object} 解析后的比赛数据
     */
    parseMatchItem(matchItem) {
        try {
            const data = matchItem.data;
            if (!data || !data.Data) {
                return null;
            }

            const match = {
                fightId: data.fightId,
                endTime: data.EndTime,
                mapId: data.MapId,
                result: data.result,
                totalPlayTime: data.TotalPlayTimeInSec,
                players: [],
                playerData: null // 当前查询玩家的数据
            };

            // 解析玩家数据
            for (const [playerId, playerData] of Object.entries(data.Data)) {
                const player = {
                    id: parseInt(playerId),
                    name: playerData.Name,
                    teamId: playerData.TeamId, // 添加队伍ID
                    dlRatio: playerData.DLRatio,
                    damageDealt: playerData.DamageDealt,
                    damageReceived: playerData.DamageReceived,
                    destruction: playerData.Destruction,
                    medals: playerData.Medals || [],
                    units: this.parseUnitData(playerData.UnitData || {}),
                    // 添加更多统计信息
                    losses: playerData.Losses,
                    rawExp: playerData.RawExp,
                    totalExp: playerData.TotalExp,
                    supplyPointsConsumed: playerData.SupplyPointsConsumed,
                    objectivesCaptured: playerData.ObjectivesCaptured || 0
                };

                match.players.push(player);
            }

            return match;

        } catch (error) {
            console.error('解析比赛数据失败:', error);
            return null;
        }
    }

    /**
     * 解析单位数据
     * @param {Object} unitData - 单位数据
     * @returns {Array} 解析后的单位数组
     */
    parseUnitData(unitData) {
        const units = [];

        for (const [unitInstanceId, unit] of Object.entries(unitData)) {
            units.push({
                instanceId: unitInstanceId,
                unitId: unit.Id,
                optionIds: unit.OptionIds || [],
                damageDealt: unit.TotalDamageDealt || 0,
                damageReceived: unit.TotalDamageReceived || 0,
                killedCount: unit.KilledCount || 0,
                supplyConsumed: unit.TotalSupplyPointsConsumed || 0,
                wasRefunded: unit.WasRefunded || false
            });
        }

        return units;
    }

    /**
     * 更新玩家统计数据
     * @param {Object} stats - 统计数据对象
     * @param {Object} match - 比赛数据
     */
    updatePlayerStats(stats, match) {
        // 这里需要根据实际的玩家ID来确定哪个是当前查询的玩家
        // 暂时使用第一个玩家的数据
        if (match.players.length > 0) {
            const player = match.players[0];
            
            stats.totalDamageDealt += player.damageDealt || 0;
            stats.totalDamageReceived += player.damageReceived || 0;

            // 统计比赛结果
            switch (match.result) {
                case 'victory':
                    stats.totalWins++;
                    break;
                case 'defeat':
                    stats.totalLosses++;
                    break;
                case 'draw':
                    stats.totalDraws++;
                    break;
            }

            // 统计单位使用情况
            player.units.forEach(unit => {
                if (!unit.wasRefunded) {
                    const count = stats.favoriteUnits.get(unit.unitId) || 0;
                    stats.favoriteUnits.set(unit.unitId, count + 1);
                }
            });

            // 统计地图数据
            const mapCount = stats.mapStats.get(match.mapId) || 0;
            stats.mapStats.set(match.mapId, mapCount + 1);
        }
    }

    /**
     * 获取单位名称映射（这里需要根据实际的单位ID映射）
     * @param {number} unitId - 单位ID
     * @returns {string} 单位名称
     */
    getUnitName(unitId) {
        // 这里应该有一个完整的单位ID到名称的映射
        // 暂时返回ID
        const unitNames = {
            1: '步兵',
            3: '反坦克步兵',
            8: '狙击手',
            20: '火箭筒',
            22: '工程师',
            // ... 更多单位映射
        };

        return unitNames[unitId] || `单位${unitId}`;
    }

    /**
     * 获取地图名称
     * @param {number} mapId - 地图ID
     * @returns {string} 地图名称
     */
    getMapName(mapId) {
        const mapNames = {
            9: '地图9',
            // ... 更多地图映射
        };

        return mapNames[mapId] || `地图${mapId}`;
    }
}

// 创建全局API实例
window.baHubAPI = new BaHubAPI();
