# BA-Hub 比赛记录分析器

一个现代化的网页应用，用于查询和分析 BA-Hub 玩家的比赛记录和单位使用统计。

## 功能特色

### 🔍 Steam ID 查询
- 支持 Steam64 ID 格式验证
- 自动保存最近查询的 Steam ID
- 提供示例 Steam ID 快速填充

### 📊 数据分析
- **玩家概览统计**: 总比赛数、胜率、总伤害、平均伤害比等
- **比赛记录列表**: 显示最近20场比赛的详细信息
- **单位使用分析**: 表格显示最常用的10个单位及使用统计
- **详细数据展示**: 每个单位的使用次数和使用率

### 🎮 比赛详情
- 点击比赛跳转到专门的详情页面
- **队伍对战分析**: 按队伍分组显示玩家数据（通常每队5人）
- **队伍统计对比**: 显示两队的总伤害、摧毁数等对比
- 详细的单位使用情况分析
- 比赛结果、时长和统计信息
- 支持多玩家标签页切换查看

### 📱 响应式设计
- 支持桌面端和移动端
- 现代化的 UI 设计
- 流畅的动画效果

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)

- **图标**: Font Awesome
- **API**: BA-Hub REST API (batrace.aoeiaol.top)

## 文件结构

```
├── index.html          # 主页面
├── match-detail.html   # 比赛详情页面
├── styles.css          # 样式文件
├── api.js             # API 调用模块

├── utils.js           # 工具函数模块
├── main.js            # 主应用程序
├── match-detail.js    # 比赛详情页面逻辑
├── test.html          # 测试页面
├── 1.json             # 示例数据文件
├── data-structure-analysis.md  # 数据结构分析
└── README.md          # 说明文档
```

## 使用方法

### 1. 启动本地服务器

```bash
# 使用 Python
python -m http.server 8000

# 或使用 Node.js
npx http-server -p 8000

# 或使用 PHP
php -S localhost:8000
```

### 2. 访问网站

在浏览器中打开: `http://localhost:8000`

### 3. 查询玩家数据

1. 在搜索框中输入有效的 Steam64 ID
2. 点击"查询"按钮或按回车键
3. 等待数据加载完成
4. 查看玩家统计和比赛记录

### 4. 查看比赛详情

- 点击任意比赛记录跳转到详情页面
- 详情页面显示完整的比赛数据
- **队伍对战视图**: 玩家按队伍分组显示（队伍1 vs 队伍2）
- **队伍统计对比**: 两队的伤害、摧毁数等数据对比
- 包括所有玩家的表现对比和详细单位分析
- 支持按玩家切换查看单位使用情况

## API 数据结构

### 元数据对象
```json
{
  "processedMatches": 0,
  "steamId": "76561198017063877",
  "totalMatches": 100,
  "type": "metadata"
}
```

### 比赛数据对象
```json
{
  "data": {
    "Data": {
      "玩家ID": {
        "DLRatio": "伤害比率",
        "DamageDealt": "造成伤害",
        "DamageReceived": "受到伤害",
        "Name": "玩家名称",
        "UnitData": {
          "单位实例ID": {
            "Id": "单位类型ID",
            "TotalDamageDealt": "单位造成伤害",
            "KilledCount": "击杀数"
          }
        }
      }
    },
    "EndTime": "结束时间戳",
    "result": "比赛结果",
    "fightId": "比赛ID"
  },
  "type": "match"
}
```

## 主要功能模块

### BaHubAPI 类
- 负责与 BA-Hub API 通信
- 数据缓存和验证
- 错误处理和超时控制



### Utils 类
- 提供各种工具函数
- 时间格式化、数字格式化
- 本地存储操作

### BaHubApp 类
- 主应用程序控制器
- 协调各个模块
- 处理用户交互

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **CORS 限制**: 由于浏览器的 CORS 政策，直接从文件系统打开 HTML 文件可能无法正常工作，建议使用本地服务器。

2. **API 限制**: BA-Hub API 可能有请求频率限制，请合理使用。

3. **数据缓存**: 应用会缓存查询结果5分钟，避免重复请求。

4. **Steam ID 格式**: 只支持 Steam64 ID 格式（17位数字，以7656119开头）。

## 示例 Steam ID

- `76561198017063877` - 示例玩家1

## 开发说明

### 添加新的单位映射

在 `api.js` 文件的 `getUnitName` 方法中添加新的单位ID映射：

```javascript
const unitNames = {
    1: '步兵',
    3: '反坦克步兵',
    // 添加更多单位...
};
```

### 添加新的图表类型

在 `charts.js` 文件的 `ChartManager` 类中添加新的图表方法：

```javascript
createNewChart(canvasId, data) {
    // 图表创建逻辑
}
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
