/**
 * 工具函数模块
 * 提供各种辅助功能
 */

class Utils {
    /**
     * 格式化时间戳为可读日期
     * @param {number} timestamp - 时间戳
     * @returns {string} 格式化的日期字符串
     */
    static formatDate(timestamp) {
        if (!timestamp) return '未知时间';
        
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天 ' + date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        } else if (diffDays === 1) {
            return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    /**
     * 格式化游戏时长
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时长字符串
     */
    static formatDuration(seconds) {
        if (!seconds || seconds < 0) return '0分钟';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else if (minutes > 0) {
            return `${minutes}分${secs}秒`;
        } else {
            return `${secs}秒`;
        }
    }

    /**
     * 格式化数字，添加千分位分隔符
     * @param {number} num - 数字
     * @returns {string} 格式化的数字字符串
     */
    static formatNumber(num) {
        if (typeof num !== 'number' || isNaN(num)) return '0';
        return num.toLocaleString('zh-CN');
    }

    /**
     * 格式化比率为百分比
     * @param {number} ratio - 比率
     * @param {number} decimals - 小数位数
     * @returns {string} 百分比字符串
     */
    static formatPercentage(ratio, decimals = 1) {
        if (typeof ratio !== 'number' || isNaN(ratio)) return '0%';
        return (ratio * 100).toFixed(decimals) + '%';
    }

    /**
     * 格式化比赛结果
     * @param {string} result - 比赛结果
     * @returns {Object} 包含文本和样式类的对象
     */
    static formatMatchResult(result) {
        const resultMap = {
            'victory': { text: '胜利', class: 'result-victory' },
            'defeat': { text: '失败', class: 'result-defeat' },
            'draw': { text: '平局', class: 'result-draw' }
        };

        return resultMap[result] || { text: '未知', class: 'result-unknown' };
    }

    /**
     * 计算胜率
     * @param {number} wins - 胜利次数
     * @param {number} total - 总次数
     * @returns {number} 胜率（0-1之间的小数）
     */
    static calculateWinRate(wins, total) {
        if (!total || total === 0) return 0;
        return wins / total;
    }

    /**
     * 获取颜色渐变
     * @param {number} value - 值
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {string} CSS颜色值
     */
    static getGradientColor(value, min, max) {
        if (max === min) return '#667eea';
        
        const ratio = (value - min) / (max - min);
        const hue = (1 - ratio) * 120; // 从红色(0)到绿色(120)
        return `hsl(${hue}, 70%, 50%)`;
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间（毫秒）
     * @returns {Function} 节流后的函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 深拷贝对象
     * @param {Object} obj - 要拷贝的对象
     * @returns {Object} 拷贝后的对象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    /**
     * 生成随机颜色
     * @returns {string} 十六进制颜色值
     */
    static getRandomColor() {
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
            '#4BC0C0', '#FF6384', '#36A2EB', '#FFCE56'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 获取图表配色方案
     * @param {number} count - 需要的颜色数量
     * @returns {Array} 颜色数组
     */
    static getChartColors(count) {
        const baseColors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c',
            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
            '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
        ];

        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(baseColors[i % baseColors.length]);
        }
        return colors;
    }

    /**
     * 排序数组
     * @param {Array} array - 要排序的数组
     * @param {string} key - 排序键
     * @param {string} order - 排序顺序 ('asc' 或 'desc')
     * @returns {Array} 排序后的数组
     */
    static sortArray(array, key, order = 'desc') {
        return array.sort((a, b) => {
            const aVal = key ? a[key] : a;
            const bVal = key ? b[key] : b;
            
            if (order === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
    }

    /**
     * 过滤数组
     * @param {Array} array - 要过滤的数组
     * @param {string} searchTerm - 搜索词
     * @param {Array} searchKeys - 搜索的键名数组
     * @returns {Array} 过滤后的数组
     */
    static filterArray(array, searchTerm, searchKeys) {
        if (!searchTerm) return array;
        
        const term = searchTerm.toLowerCase();
        return array.filter(item => {
            return searchKeys.some(key => {
                const value = key.split('.').reduce((obj, k) => obj && obj[k], item);
                return value && value.toString().toLowerCase().includes(term);
            });
        });
    }

    /**
     * 显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success', 'error', 'warning', 'info')
     * @param {number} duration - 显示时长（毫秒）
     */
    static showNotification(message, type = 'info', duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        // 设置背景色
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }

    /**
     * 本地存储操作
     */
    static storage = {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('存储数据失败:', e);
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取数据失败:', e);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('删除数据失败:', e);
            }
        }
    };
}
