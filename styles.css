/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.title i {
    margin-right: 15px;
    color: #ffd700;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 卡片样式 */
.search-card, .info-card, .chart-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-card h2, .info-card h3, .chart-card h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.search-card h2 i, .info-card h3 i {
    margin-right: 10px;
    color: #667eea;
}

/* 搜索表单 */
.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.steam-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.steam-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 示例按钮 */
.example-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.example-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: monospace;
}

.example-btn:hover {
    background: #667eea;
    color: white;
}

/* 错误消息 */
.error-message {
    background: #fee;
    color: #c33;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #c33;
    margin-top: 15px;
}

/* 结果区域 */
.results-section {
    margin-top: 40px;
}

.player-overview {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 比赛列表 */
.matches-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.match-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.match-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.match-result {
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.result-victory { background: #d4edda; color: #155724; }
.result-defeat { background: #f8d7da; color: #721c24; }
.result-draw { background: #fff3cd; color: #856404; }

/* 单位分析容器 */
.units-analysis {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.analysis-container {
    margin-top: 20px;
}

.unit-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.unit-stat-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.unit-name {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #333;
}

.unit-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.unit-detail {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

.unit-detail-value {
    font-weight: 500;
    color: #333;
}

/* 信息列表 */
.info-list {
    list-style: none;
    margin-bottom: 20px;
}

.info-list li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
}

.info-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

/* 功能标签 */
.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.tag i {
    margin-right: 5px;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 30px;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
}

.close:hover {
    opacity: 1;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 50px;
    padding: 20px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .search-btn {
        width: 100%;
    }
    
    .overview-stats {
        grid-template-columns: 1fr;
    }
    
    .unit-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner i {
    animation: spin 1s linear infinite;
}
