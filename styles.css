/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.title i {
    margin-right: 15px;
    color: #ffd700;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 卡片样式 */
.search-card, .info-card, .chart-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-card h2, .info-card h3, .chart-card h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.search-card h2 i, .info-card h3 i {
    margin-right: 10px;
    color: #667eea;
}

/* 搜索表单 */
.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.steam-input {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.steam-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 示例按钮 */
.example-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.example-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: monospace;
}

.example-btn:hover {
    background: #667eea;
    color: white;
}

/* 错误消息 */
.error-message {
    background: #fee;
    color: #c33;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #c33;
    margin-top: 15px;
}

/* 结果区域 */
.results-section {
    margin-top: 40px;
}

.player-overview {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 比赛列表 */
.matches-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.match-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.match-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.match-result {
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.result-victory { background: #d4edda; color: #155724; }
.result-defeat { background: #f8d7da; color: #721c24; }
.result-draw { background: #fff3cd; color: #856404; }

/* 单位分析容器 */
.units-analysis {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.analysis-container {
    margin-top: 20px;
}

.unit-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.unit-stat-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #667eea;
}

.unit-name {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #333;
}

.unit-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.unit-detail {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

.unit-detail-value {
    font-weight: 500;
    color: #333;
}

/* 信息列表 */
.info-list {
    list-style: none;
    margin-bottom: 20px;
}

.info-list li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
}

.info-list li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

/* 功能标签 */
.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.tag i {
    margin-right: 5px;
}



/* 页脚 */
.footer {
    text-align: center;
    margin-top: 50px;
    padding: 20px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .search-btn {
        width: 100%;
    }
    
    .overview-stats {
        grid-template-columns: 1fr;
    }
    
    .unit-stats-grid {
        grid-template-columns: 1fr;
    }
    

}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner i {
    animation: spin 1s linear infinite;
}

/* 比赛详情页面样式 */
.navigation {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.match-header-info {
    display: flex;
    align-items: center;
    gap: 20px;
    color: white;
    flex-wrap: wrap;
}

.match-id {
    font-weight: bold;
    font-size: 1.1rem;
}

.loading-section {
    text-align: center;
    padding: 60px 20px;
}

.loading-spinner-large {
    display: inline-block;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.error-section {
    text-align: center;
    padding: 60px 20px;
}

.error-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    max-width: 500px;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.error-card h2 {
    color: #dc3545;
    margin-bottom: 15px;
}

.back-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 比赛概览 */
.match-overview {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-label {
    font-weight: 500;
    color: #666;
}

.info-value {
    font-weight: bold;
    color: #333;
}

/* 玩家对比 */
.players-comparison {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.players-grid {
    margin-top: 20px;
}

.teams-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.team-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border: 2px solid #e9ecef;
}

.team-section:first-child {
    border-color: #667eea;
}

.team-section:last-child {
    border-color: #f5576c;
}

.team-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #dee2e6;
}

.team-header h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.3rem;
}

.team-stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.team-stats span {
    background: white;
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.team-players {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.player-card {
    background: white;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.team-section:last-child .player-card {
    border-left-color: #f5576c;
}

.player-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.player-name {
    margin: 0 0 3px 0;
    color: #333;
    font-size: 1.1rem;
}

.player-id {
    color: #666;
    font-size: 0.8rem;
}

.player-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.stat-label {
    color: #666;
}

.stat-value {
    font-weight: 500;
    color: #333;
}

/* 单位详细分析 */
.units-detailed-analysis {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.players-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.player-tab {
    padding: 10px 20px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.player-tab:hover {
    border-color: #667eea;
    background: #f8f9fa;
}

.player-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.units-summary {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.units-summary h3 {
    margin: 0 0 5px 0;
    color: #333;
}

.units-summary p {
    margin: 0;
    color: #666;
}

.units-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
    padding: 15px 10px;
    gap: 10px;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    padding: 15px 10px;
    gap: 10px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background: #f8f9fa;
}

.table-row:last-child {
    border-bottom: none;
}

.col-unit {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.unit-name {
    font-weight: 500;
    color: #333;
}

.unit-id {
    font-size: 0.8rem;
    color: #666;
}

.col-damage, .col-received, .col-kills, .col-supply {
    text-align: right;
    font-weight: 500;
    color: #333;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

/* 比赛统计 */
.match-statistics {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.stat-card.team1-stat {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.team2-stat {
    background: linear-gradient(135deg, #f5576c 0%, #f093fb 100%);
}

.stat-title {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-detail {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* 响应式设计 - 比赛详情页面 */
@media (max-width: 768px) {
    .navigation {
        flex-direction: column;
        text-align: center;
    }

    .match-header-info {
        justify-content: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .teams-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .player-stats {
        grid-template-columns: 1fr;
    }

    .players-tabs {
        justify-content: center;
    }

    .table-header, .table-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .table-header > div, .table-row > div {
        text-align: left;
        padding: 5px 0;
    }

    .col-unit {
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        margin-bottom: 10px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
